// Utility functions for admin styling and role management

export const isAdmin = (role?: string) => ['admin', 'developer', 'moderator'].includes(role || '')

export const getAdminNameClasses = (role?: string, baseClasses?: string) => {
  if (!isAdmin(role)) {
    return baseClasses || 'font-bold text-lg transition'
  }
  
  return 'font-bold transition bg-gradient-to-r from-yellow-400 via-orange-400 to-red-400 bg-clip-text text-transparent animate-pulse drop-shadow-[0_0_8px_rgba(251,191,36,0.6)]'
}

export const getRoleDisplayName = (role?: string) => {
  switch (role) {
    case 'admin': return 'Admin'
    case 'developer': return 'Developer'
    case 'moderator': return 'Moderator'
    default: return null
  }
}

export const getRoleBadgeClasses = () => {
  return 'text-xs px-2 py-1 bg-gradient-to-r from-yellow-500/20 to-orange-500/20 text-yellow-300 rounded-full border border-yellow-500/30'
}
