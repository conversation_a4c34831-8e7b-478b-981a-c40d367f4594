// Utility functions for admin styling and role management

export const isAdmin = (role?: string) => ['creator', 'support'].includes(role || '')

export const getAdminNameClasses = (role?: string, baseClasses?: string) => {
  if (!isAdmin(role)) {
    return baseClasses || 'font-bold text-lg transition'
  }

  return 'font-bold transition bg-gradient-to-r from-yellow-400 via-orange-400 to-red-400 bg-clip-text text-transparent animate-pulse drop-shadow-[0_0_8px_rgba(251,191,36,0.6)]'
}

export const getRoleDisplayName = (role?: string) => {
  switch (role) {
    case 'creator': return 'Creator'
    case 'support': return 'Support'
    default: return null
  }
}

export const getRoleBadgeClasses = (role?: string) => {
  switch (role) {
    case 'creator': return 'text-xs px-2 py-1 bg-orange-500/80 text-white rounded-full'
    case 'support': return 'text-xs px-2 py-1 bg-green-500/80 text-white rounded-full'
    default: return 'text-xs px-2 py-1 bg-gray-500/80 text-white rounded-full'
  }
}
