import { sanity, urlFor } from '../lib/sanity'
import Image from 'next/image'
import Link from 'next/link'
import Head from 'next/head'
import 'flag-icons/css/flag-icons.min.css'
import { useLanguage } from '../lib/LanguageContext'
import { useState, useMemo, useEffect } from 'react'
import { useRouter } from 'next/router'
import { isAdmin, getAdminNameClasses, getRoleDisplayName, getRoleBadgeClasses } from '../lib/adminUtils'

interface Player {
  _id: string;
  name: string;
  nickname: string;
  elo: number;
  image: any;
  country: string;
  team: {
    name: string;
    _id: string;
  };
  city?: string;
  age?: number;
  discord?: string;
  role?: string;
}

export async function getStaticProps() {
  console.log('PLAYERS.TSX: getStaticProps triggered at ', new Date().toISOString());

  const players = await sanity.fetch(`*[_type == "player" && !(_id in path("drafts.**"))] | order(elo desc){
    _id,
    name,
    nickname,
    elo,
    image,
    country,
    team->{
      name,
      _id
    },
    city,
    age,
    discord,
    role
  }`)
  console.log('PLAYERS.TSX: Fetched players count:', players?.length);
  if (players?.length > 0) {
    console.log('PLAYERS.TSX: First player fetched:', JSON.stringify(players[0])); // Log the first player as an example
  }
  return {
    props: {
      players,
      lastUpdated: new Date().toISOString() // Add timestamp to force cache invalidation
    },
    revalidate: 60, // Revalidate every 60 seconds
  }
}

export default function PlayersPage({ players, lastUpdated }: { players: Player[], lastUpdated?: string }) {
  const { t } = useLanguage();
  const router = useRouter();
  const [selectedPlayer, setSelectedPlayer] = useState<Player | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [toastMessage, setToastMessage] = useState<string | null>(null);

  const handleCopyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      setToastMessage(t("toast_copy_success"));
    }).catch(err => {
      console.error('Failed to copy: ', err);
      setToastMessage(t("toast_copy_failed"));
    });
  };

  // Handle query parameter to auto-open player modal
  useEffect(() => {
    if (router.isReady && router.query.open && players.length > 0) {
      const playerId = router.query.open as string;
      const player = players.find(p => p._id === playerId);
      if (player) {
        setSelectedPlayer(player);
        // Clean up URL without triggering navigation
        router.replace('/players', undefined, { shallow: true });
      }
    }
  }, [router.isReady, router.query.open, players]);

  useEffect(() => {
    if (toastMessage) {
      const timer = setTimeout(() => {
        setToastMessage(null);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [toastMessage]);

  // Function to navigate to teams page with auto-open team modal
  const navigateToTeam = (teamId: string) => {
    router.push(`/teams?open=${teamId}`);
  };

  const filteredPlayers = useMemo(() => {
    if (!searchTerm) {
      return players;
    }
    return players.filter(player =>
      player.nickname.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [players, searchTerm]);

  return (
    <>
      <Head>
        <title>{`${t('players_page_title')} - QRP HLTV`}</title>
        <meta name="description" content="CarX Drift Racing pilots rankings, statistics and profiles" />
        <meta property="og:title" content={`${t('players_page_title')} - QRP HLTV`} />
        <meta property="og:description" content="CarX Drift Racing pilots rankings, statistics and profiles" />
        <meta property="og:url" content="https://qrp-hltv.com/players" />
        <meta name="twitter:title" content={`${t('players_page_title')} - QRP HLTV`} />
        <meta name="twitter:description" content="CarX Drift Racing pilots rankings, statistics and profiles" />
      </Head>
      <main className="max-w-6xl mx-auto py-12 px-4">
        <div className="flex items-center mb-8">
          <h1 className="text-3xl font-bold mr-4">{t('players_page_title')}</h1>
          <div className="flex-1 h-1 bg-purple-700 rounded-full opacity-40" />
        </div>
        <div className="mb-8">
          <input
            type="text"
            placeholder={t('search_nickname_placeholder') || "Search by nickname..."}
            className="w-full px-4 py-2 rounded-lg bg-zinc-800 border border-zinc-700 focus:ring-2 focus:ring-purple-600 focus:border-purple-600 outline-none transition"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredPlayers.map((player) => {
            const rank = players.findIndex(p => p._id === player._id);
            return (
              <div
                key={player._id}
                className="bg-zinc-900 rounded-xl border border-zinc-800 overflow-hidden hover:border-purple-600 transition group cursor-pointer"
                onClick={() => setSelectedPlayer(player)}
              >
                <div className="aspect-square relative overflow-hidden bg-zinc-800">
                  {rank === 0 && (
                    <span className="absolute top-2 right-2 text-3xl z-10" style={{textShadow: '0 0 8px rgba(0,0,0,0.8)'}} title="Rank 1">🥇</span>
                  )}
                  {rank === 1 && (
                    <span className="absolute top-2 right-2 text-3xl z-10" style={{textShadow: '0 0 8px rgba(0,0,0,0.8)'}} title="Rank 2">🥈</span>
                  )}
                  {rank === 2 && (
                    <span className="absolute top-2 right-2 text-3xl z-10" style={{textShadow: '0 0 8px rgba(0,0,0,0.8)'}} title="Rank 3">🥉</span>
                  )}
                  {rank > 2 && (
                    <span
                      className="absolute top-2 right-2 text-base font-semibold z-10 text-zinc-300 px-1.5 py-0.5 bg-black/50 rounded-md"
                      style={{textShadow: '0 0 6px rgba(0,0,0,0.7)'}}
                      title={`Rank ${rank + 1}`}
                    >
                      #{rank + 1}
                    </span>
                  )}
                  {player.image ? (
                    <Image
                      src={urlFor(player.image).width(400).height(400).url()}
                      alt={player.name}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-zinc-600">
                      {t('no_image_label')}
                    </div>
                  )}
                </div>
                <div className="p-4">
                  <div className="flex items-center gap-2">
                    <h3 className={getAdminNameClasses(player.role, 'font-bold text-lg group-hover:text-purple-400 transition')}>{player.nickname}</h3>
                    {getRoleDisplayName(player.role) && (
                      <span className={getRoleBadgeClasses()}>
                        {getRoleDisplayName(player.role)}
                      </span>
                    )}
                    {player.country && (
                      <span className={`fi fi-${player.country.toLowerCase()} text-sm`}
                            title={player.country} />
                    )}
                  </div>
                  <p className="text-zinc-400 text-sm">{player.name}</p>
                  <div className="mt-2 flex items-center justify-between">
                    <span className="text-purple-400 font-semibold">{t('elo_label')} {player.elo}</span>
                    {player.team && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation(); // Prevent opening player modal
                          navigateToTeam(player.team._id);
                        }}
                        className="text-zinc-500 hover:text-orange-400 text-sm transition cursor-pointer"
                        title={`View ${player.team.name} team`}
                      >
                        {player.team.name}
                      </button>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {selectedPlayer && (
          <div
            className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4 backdrop-blur-sm"
            onClick={() => setSelectedPlayer(null)}
          >
            <div
              className="bg-zinc-900 rounded-2xl p-6 max-w-md w-full max-h-[90vh] overflow-y-auto border border-zinc-700 shadow-2xl"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex justify-between items-start mb-4">
                <div className="flex items-center gap-3">
                  <h2 className={getAdminNameClasses(selectedPlayer.role, 'text-2xl font-bold text-purple-400')}>
                    {selectedPlayer.nickname}
                  </h2>
                  {getRoleDisplayName(selectedPlayer.role) && (
                    <span className={getRoleBadgeClasses()}>
                      {getRoleDisplayName(selectedPlayer.role)}
                    </span>
                  )}
                </div>
                <button
                  onClick={() => setSelectedPlayer(null)}
                  className="text-zinc-400 hover:text-zinc-100 transition p-1 rounded-md hover:bg-zinc-700"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div className="space-y-3">
                {selectedPlayer.image && (
                  <div className="aspect-square relative overflow-hidden bg-zinc-800 rounded-lg mb-4">
                    <Image
                      src={urlFor(selectedPlayer.image).width(400).height(400).url()}
                      alt={selectedPlayer.name}
                      fill
                      className="object-cover"
                    />
                  </div>
                )}
                <p><span className="font-semibold text-zinc-400">{t('real_name_label')}:</span> {selectedPlayer.name}</p>
                <p><span className="font-semibold text-zinc-400">{t('elo_label')}:</span> {selectedPlayer.elo}</p>
                {selectedPlayer.country && (
                  <p className="flex items-center">
                    <span className="font-semibold text-zinc-400 mr-2">{t('country_label')}:</span>
                    <span className={`fi fi-${selectedPlayer.country.toLowerCase()} mr-1`} title={selectedPlayer.country} />
                    {selectedPlayer.country}
                  </p>
                )}
                {selectedPlayer.team && (
                  <p>
                    <span className="font-semibold text-zinc-400">{t('team_label')}:</span>{' '}
                    <button
                      onClick={() => navigateToTeam(selectedPlayer.team._id)}
                      className="text-zinc-200 hover:text-orange-400 transition cursor-pointer underline"
                      title={`View ${selectedPlayer.team.name} team`}
                    >
                      {selectedPlayer.team.name}
                    </button>
                  </p>
                )}
                {selectedPlayer.city && (
                  <p><span className="font-semibold text-zinc-400">{t('city_label')}:</span> {selectedPlayer.city}</p>
                )}
                {selectedPlayer.age && (
                  <p><span className="font-semibold text-zinc-400">{t('age_label')}:</span> {selectedPlayer.age}</p>
                )}
                {selectedPlayer.discord && (
                  <div className="flex items-center">
                    <button
                      onClick={() => handleCopyToClipboard(selectedPlayer.discord!)}
                      className="text-zinc-400 hover:text-purple-400 transition p-1 rounded-md hover:bg-zinc-700 flex items-center"
                      title={t('copy_discord_tip')}
                    >
                      <svg className="h-5 w-5 mr-2" role="img" viewBox="0 -28.5 256 256" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" preserveAspectRatio="xMidYMid" fill="currentColor">
                        <path d="M216.856339,16.5966031 C200.285002,8.84328665 182.566144,3.2084988 164.041564,0 C161.766523,4.11318106 159.108624,9.64549908 157.276099,14.0464379 C137.583995,11.0849896 118.072967,11.0849896 98.7430163,14.0464379 C96.9108417,9.64549908 94.1925838,4.11318106 91.8971895,0 C73.3526068,3.2084988 55.6133949,8.86399117 39.0420583,16.6376612 C5.61752293,67.146514 -3.4433191,116.400813 1.08711069,164.955721 C23.2560196,181.510915 44.7403634,191.567697 65.8621325,198.148576 C71.0772151,190.971126 75.7283628,183.341335 79.7352139,175.300261 C72.104019,172.400575 64.7949724,168.822202 57.8887866,164.667963 C59.7209612,163.310589 61.5131304,161.891452 63.2445898,160.431257 C105.36741,180.133187 151.134928,180.133187 192.754523,160.431257 C194.506336,161.891452 196.298154,163.310589 198.110326,164.667963 C191.183787,168.842556 183.854737,172.420929 176.223542,175.320965 C180.230393,183.341335 184.861538,190.991831 190.096624,198.16893 C211.238746,191.588051 232.743023,181.531619 254.911949,164.955721 C260.227747,108.668201 245.831087,59.8662432 216.856339,16.5966031 Z M85.4738752,135.09489 C72.8290281,135.09489 62.4592217,123.290155 62.4592217,108.914901 C62.4592217,94.5396472 72.607595,82.7145587 85.4738752,82.7145587 C98.3405064,82.7145587 108.709962,94.5189427 108.488529,108.914901 C108.508531,123.290155 98.3405064,135.09489 85.4738752,135.09489 Z M170.525237,135.09489 C157.88039,135.09489 147.510584,123.290155 147.510584,108.914901 C147.510584,94.5396472 157.658606,82.7145587 170.525237,82.7145587 C183.391518,82.7145587 193.761324,94.5189427 193.539891,108.914901 C193.539891,123.290155 183.391518,135.09489 170.525237,135.09489 Z" fillRule="nonzero"></path>
                      </svg>
                      <span className="text-zinc-200">{selectedPlayer.discord}</span>
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </main>
      {toastMessage && (
        <div
          className="fixed top-5 right-5 bg-zinc-700 text-zinc-100 py-2 px-4 rounded-md shadow-lg transition-opacity duration-300 z-[9999]"
          aria-live="assertive"
          role="status"
        >
          {toastMessage}
        </div>
      )}
    </>
  )
}